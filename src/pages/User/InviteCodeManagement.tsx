import { useState } from "react";
import { useTranslation } from "react-i18next";
import toast from "react-hot-toast";
import PageBreadcrumb from "@components/common/PageBreadCrumb";
import PageMeta from "@components/common/PageMeta";
import ComponentCard from "@components/common/ComponentCard";
import Label from "@components/form/Label";
import InputField from "@components/form/input/InputField";
import Select from "@components/ui/select/Selection";
import DatePicker from "@components/form/date-picker";
import Button from "@components/ui/button/Button";
import Badge from "@components/ui/badge/Badge";
import { Table, TableBody, TableCell, TableHeader, TableRow } from "@components/ui/table";
import { ChevronUpIcon } from "@assets/icons";
import { useCopyToClipboard } from "@hooks/useCopyToClipboard";

// 邀请码数据类型定义
interface InviteCodeData {
  id: number;
  inviteCode: string;
  inviterName: string;
  inviterEmail: string;
  userName: string;
  userEmail: string;
  region: string;
  registerTime: string;
  usedCount: number;
  totalCount: number;
  reward: number;
  status: "active" | "expired" | "used";
}

// 模拟邀请码数据
const mockInviteCodeData: InviteCodeData[] = [
  {
    id: 1,
    inviteCode: "**********",
    inviterName: "张三",
    inviterEmail: "<EMAIL>",
    userName: "李四",
    userEmail: "<EMAIL>",
    region: "中国",
    registerTime: "2025-01-15 10:30:00",
    usedCount: 1,
    totalCount: 3,
    reward: 100,
    status: "active",
  },
  {
    id: 2,
    inviteCode: "INV2024002",
    inviterName: "王五",
    inviterEmail: "<EMAIL>",
    userName: "赵六",
    userEmail: "<EMAIL>",
    region: "新加坡",
    registerTime: "2025-01-14 15:45:00",
    usedCount: 3,
    totalCount: 3,
    reward: 300,
    status: "used",
  },
  {
    id: 3,
    inviteCode: "INV2024003",
    inviterName: "小明",
    inviterEmail: "<EMAIL>",
    userName: "小红",
    userEmail: "<EMAIL>",
    region: "美国",
    registerTime: "2025-01-13 09:20:00",
    usedCount: 0,
    totalCount: 5,
    reward: 0,
    status: "active",
  },
  {
    id: 4,
    inviteCode: "INV2024004",
    inviterName: "老李",
    inviterEmail: "<EMAIL>",
    userName: "小王",
    userEmail: "<EMAIL>",
    region: "英国",
    registerTime: "2025-01-12 14:15:00",
    usedCount: 2,
    totalCount: 3,
    reward: 200,
    status: "active",
  },
  {
    id: 5,
    inviteCode: "INV2024005",
    inviterName: "刘老师",
    inviterEmail: "<EMAIL>",
    userName: "学生A",
    userEmail: "<EMAIL>",
    region: "加拿大",
    registerTime: "2025-01-11 16:30:00",
    usedCount: 1,
    totalCount: 1,
    reward: 100,
    status: "used",
  },
];

export default function InviteCodeManagement() {
  const { t } = useTranslation();
  const [collapsed, setCollapsed] = useState(false);

  // 使用复制到剪贴板 hook
  const { copyToClipboard } = useCopyToClipboard({
    successMessage: t("pages.inviteCodeManagement.messages.copied"),
    errorMessage: t("pages.inviteCodeManagement.messages.copyFailed"),
  });

  const [queryForm, setQueryForm] = useState({
    conditionField: "",
    conditionValue: "",
    startDate: "",
    endDate: "",
    region: "",
  });

  // 条件查询字段选项
  const conditionFieldOptions = [
    { value: "id", label: t("pages.inviteCodeManagement.conditionField.id") },
    {
      value: "userName",
      label: t("pages.inviteCodeManagement.conditionField.userName"),
    },
    {
      value: "userEmail",
      label: t("pages.inviteCodeManagement.conditionField.userEmail"),
    },
    {
      value: "inviteCode",
      label: t("pages.inviteCodeManagement.conditionField.inviteCode"),
    },
  ];

  // 地区选项
  const regionOptions = [
    { value: "china", label: t("pages.inviteCodeManagement.regions.china") },
    {
      value: "singapore",
      label: t("pages.inviteCodeManagement.regions.singapore"),
    },
    { value: "usa", label: t("pages.inviteCodeManagement.regions.usa") },
    { value: "uk", label: t("pages.inviteCodeManagement.regions.uk") },
    { value: "canada", label: t("pages.inviteCodeManagement.regions.canada") },
    {
      value: "hongkong",
      label: t("pages.inviteCodeManagement.regions.hongkong"),
    },
  ];

  const handleQuery = () => {
    console.log("Query:", queryForm);
    // TODO: 处理查询逻辑
    toast.success(t("pages.inviteCodeManagement.messages.queryComplete"), {
      duration: 2000,
      icon: "🔍",
    });
  };

  const handleReset = () => {
    setQueryForm({
      conditionField: "",
      conditionValue: "",
      startDate: "",
      endDate: "",
      region: "",
    });

    toast(t("pages.inviteCodeManagement.messages.formReset"), {
      icon: "🔄",
      duration: 1500,
    });
  };

  const handleCreateInviteCode = () => {
    // TODO: 处理新建邀请码逻辑
    toast.success(t("pages.inviteCodeManagement.messages.createInviteCodeInDevelopment"), {
      duration: 2000,
      icon: "➕",
    });
  };

  const handleConditionFieldChange = (value: string) => {
    setQueryForm({ ...queryForm, conditionField: value });
  };

  const handleRegionChange = (value: string) => {
    setQueryForm({ ...queryForm, region: value });
  };

  const handleViewDetails = (inviteCode: string) => {
    // TODO: 处理查看详情逻辑
    toast(
      t("pages.inviteCodeManagement.messages.viewingDetails", {
        code: inviteCode,
      }),
      {
        icon: "👁️",
        duration: 2000,
      }
    );
  };

  const getStatusBadge = (status: InviteCodeData["status"]) => {
    switch (status) {
      case "active":
        return (
          <Badge size="sm" color="success">
            {t("pages.inviteCodeManagement.status.active")}
          </Badge>
        );
      case "used":
        return (
          <Badge size="sm" color="info">
            {t("pages.inviteCodeManagement.status.used")}
          </Badge>
        );
      case "expired":
        return (
          <Badge size="sm" color="error">
            {t("pages.inviteCodeManagement.status.expired")}
          </Badge>
        );
      default:
        return (
          <Badge size="sm" color="warning">
            {t("pages.inviteCodeManagement.status.unknown")}
          </Badge>
        );
    }
  };

  return (
    <>
      <PageMeta
        title={t("pages.inviteCodeManagement.title")}
        description={t("pages.inviteCodeManagement.description")}
      />
      <PageBreadcrumb pageTitle={t("pages.inviteCodeManagement.breadcrumb")} />

      <div className="space-y-6">
        {/* 查询条件区域 */}
        <ComponentCard title="">
          <div className={`space-y-4 ${collapsed ? "hidden" : ""}`}>
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* 条件查询 - 字段选择 + 输入框 */}
              <div className="md:col-span-2">
                <Label htmlFor="condition">{t("pages.inviteCodeManagement.conditionQuery")}</Label>
                <div className="flex gap-2">
                  <div className="w-36">
                    <Select
                      options={conditionFieldOptions}
                      placeholder={t("pages.inviteCodeManagement.conditionField.select")}
                      onChange={handleConditionFieldChange}
                      value={queryForm.conditionField}
                      size="md"
                      clearable
                    />
                  </div>
                  <div className="flex-1">
                    <InputField
                      type="text"
                      id="condition-value"
                      placeholder={t("pages.inviteCodeManagement.conditionValue.placeholder")}
                      value={queryForm.conditionValue}
                      onChange={e =>
                        setQueryForm({
                          ...queryForm,
                          conditionValue: e.target.value,
                        })
                      }
                    />
                  </div>
                </div>
              </div>

              {/* 注册时间 - 开始日期 */}
              <div>
                <DatePicker
                  id="start-date"
                  label={`${t("pages.inviteCodeManagement.registerTime")} (${t("common.start")})`}
                  placeholder="2025-01-01"
                  onChange={(_dates, currentDateString) => {
                    setQueryForm({
                      ...queryForm,
                      startDate: currentDateString,
                    });
                  }}
                />
              </div>

              {/* 注册时间 - 结束日期 */}
              <div>
                <DatePicker
                  id="end-date"
                  label={`${t("pages.inviteCodeManagement.registerTime")} (${t("common.end")})`}
                  placeholder="2025-01-31"
                  onChange={(_dates, currentDateString) => {
                    setQueryForm({ ...queryForm, endDate: currentDateString });
                  }}
                />
              </div>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              {/* 地区 */}
              <div>
                <Label>{t("pages.inviteCodeManagement.region")}</Label>
                <Select
                  options={regionOptions}
                  placeholder={t("pages.inviteCodeManagement.regions.all")}
                  onChange={handleRegionChange}
                  value={queryForm.region}
                  size="md"
                  clearable
                />
              </div>
            </div>
          </div>

          {/* 按钮区域 */}
          <div className="flex justify-between gap-3 mt-6">
            <div>
              <Button onClick={handleCreateInviteCode} variant="primary" size="sm">
                {t("pages.inviteCodeManagement.createInviteCode")}
              </Button>
            </div>
            <div className="flex gap-3">
              <Button onClick={handleReset} variant="outline" size="sm">
                {t("common.reset")}
              </Button>
              <Button onClick={handleQuery} size="sm">
                {t("pages.inviteCodeManagement.query")}
              </Button>
              <Button
                onClick={() => setCollapsed(!collapsed)}
                variant="outline"
                size="sm"
                endIcon={
                  <ChevronUpIcon
                    className={`size-4 transition-transform ${collapsed ? "rotate-180" : ""}`}
                  />
                }
              >
                {t("pages.inviteCodeManagement.collapse")}
              </Button>
            </div>
          </div>
        </ComponentCard>

        {/* 邀请码列表表格 */}
        <ComponentCard title="">
          <div className="overflow-hidden rounded-xl border border-gray-200 bg-white dark:border-white/[0.05] dark:bg-white/[0.03]">
            <div className="max-w-full overflow-x-auto">
              <Table>
                <TableHeader className="border-b border-gray-100 dark:border-white/[0.05]">
                  <TableRow>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.inviteCodeManagement.inviteCode")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.inviteCodeManagement.inviter")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.inviteCodeManagement.userName")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.inviteCodeManagement.userEmail")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.inviteCodeManagement.region")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.inviteCodeManagement.registerTime")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.inviteCodeManagement.usedInviteCodes")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.inviteCodeManagement.reward")}
                    </TableCell>
                    <TableCell
                      isHeader
                      className="px-5 py-3 font-medium text-gray-500 text-start text-theme-xs dark:text-gray-400"
                    >
                      {t("pages.inviteCodeManagement.actions")}
                    </TableCell>
                  </TableRow>
                </TableHeader>

                <TableBody className="divide-y divide-gray-100 dark:divide-white/[0.05]">
                  {mockInviteCodeData.map(invite => (
                    <TableRow key={invite.id}>
                      <TableCell className="px-5 py-4 sm:px-6 text-start">
                        <span
                          className="font-mono text-brand-500 font-medium hover:underline cursor-pointer"
                          onClick={() => copyToClipboard(invite.inviteCode)}
                        >
                          {invite.inviteCode}
                        </span>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-800 dark:text-white/90">
                            {invite.inviterName}
                          </div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            {invite.inviterEmail}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <span className="font-medium text-gray-800 dark:text-white/90">
                          {invite.userName}
                        </span>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <span
                          className="text-brand-500 hover:underline cursor-pointer"
                          onClick={() => copyToClipboard(invite.userEmail)}
                        >
                          {invite.userEmail}
                        </span>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {invite.region}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-gray-500 text-start text-theme-sm dark:text-gray-400">
                        {invite.registerTime}
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-gray-800 dark:text-white/90">
                            {invite.usedCount}/{invite.totalCount}
                          </span>
                          {getStatusBadge(invite.status)}
                        </div>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <span className="font-medium text-green-600 dark:text-green-400">
                          ¥{invite.reward}
                        </span>
                      </TableCell>
                      <TableCell className="px-4 py-3 text-start text-theme-sm">
                        <button
                          className="text-brand-500 hover:underline"
                          onClick={() => handleViewDetails(invite.inviteCode)}
                        >
                          {t("pages.inviteCodeManagement.details")}
                        </button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </ComponentCard>
      </div>
    </>
  );
}
